package com.agora.event;

import com.agora.commons.CustomerCommons;
import com.agora.commons.EntityNotificationCommons;
import com.agora.commons.EntityNotificationCommons.EventNotificationType;
import com.agora.commons.EventCommons;
import com.agora.commons.QrcodeCommons;
import com.agora.commons.NotificationCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.dao.EventDao;
import com.agora.dao.EventFollowerDao;
import com.agora.dao.EventNotificationDao;
import com.agora.dao.EventReportDao;
import com.agora.dao.PageDao;
import com.agora.dao.UserDao;
import com.agora.pojo.Customer;
import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.EventNotification;
import com.agora.pojo.EventReport;
import com.agora.pojo.Location;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import com.agora.pojo.types.EventStatusType;
import com.agora.pojo.types.ProfileType;
import com.agora.support.Geocoder;
import com.agora.support.ProvinceEntry;
import com.agora.support.file.posted.PostedFile;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class EventController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventController.class.getName());

    public static TemplateViewRoute event_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("pageId"));
        attributes.put("fromPageId", pageId);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        attributes.put("backUrl", request.queryParams("backUrl"));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // nuova gestione container
        if (request.queryParams("isContainer") != null) {
            boolean isContainer = BooleanUtils.toBoolean(request.queryParams("isContainer"));
            attributes.put("isContainer", isContainer);
        }
        if (request.queryParams("parentId") != null) {
            ObjectId parentId = ParamUtils.toObjectId(request.queryParams("parentId"));
            Event parent = EventDao.loadEvent(parentId);
            if (parent != null) {
                attributes.put("parent", parent);
            }
        } else {
            // verifico se l'utente ha creato almeno un container
            attributes.put("hasContainer", EventDao.userHasContainer(user.getId()));
        }

        return Manager.render(Templates.EVENT_ADD, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute event_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        Event event = EventDao.loadEvent(oid);

        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            if (!event.getOwnerId().equals(user.getId())) {
                response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            }
        }
        attributes.put("event", event);
        attributes.put("isContainer", StringUtils.equalsIgnoreCase(event.getType(), "container"));
        if (event.getParentId() != null) {
            Event parent = EventDao.loadEvent(event.getParentId());
            if (parent != null) {
                attributes.put("parent", parent);
            }
        } else {
            // verifico se l'utente ha creato almeno un container
            attributes.put("hasContainer", EventDao.userHasContainer(user.getId()));
        }
//        String pageType = event.getPageType();
//
//        if (ParamUtils.emptyToNull(request.queryParams("pageType")) != null) {
//            pageType = request.queryParams("pageType");
//        }
//        attributes.put("pageType", pageType);
        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        attributes.put("backUrl", request.queryParams("backUrl"));

        return Manager.render(Templates.EVENT_EDIT, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute event_clone = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        Event event = EventDao.loadEvent(oid);
        if (!event.getOwnerId().equals(user.getId())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
        }

        event.setOriginalId(oid);
        event.setId(null);
        event.setCreation(null);
        event.setLastUpdate(null);
        event.setCancelled(null);
        event.setIdentifier(null);
        event.setQrcode(null);
        event.setQrcodeFileId(null);
        attributes.put("event", event);

        attributes.put("isContainer", StringUtils.equalsIgnoreCase(event.getType(), "container"));
        if (event.getParentId() != null) {
            Event parent = EventDao.loadEvent(event.getParentId());
            if (parent != null) {
                attributes.put("parent", parent);
            }
        } else {
            // verifico se l'utente ha creato almeno un container
            attributes.put("hasContainer", EventDao.userHasContainer(user.getId()));
        }

//        String pageType = event.getPageType();
//
//        if (ParamUtils.emptyToNull(request.queryParams("pageType")) != null) {
//            pageType = request.queryParams("pageType");
//        }
//        attributes.put("pageType", pageType);
        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.EVENT_CLONE, attributes, RouteUtils.pathType(request));
    };

    public static Route event_add_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        // merge
        Event tmpEvent = PojoUtils.createFromParams(params, Event.class);
        if (StringUtils.isBlank(params.get("freeEntry"))) {
            tmpEvent.setFreeEntry(false);
        }
        if (StringUtils.isBlank(params.get("showFollowers"))) {
            tmpEvent.setShowFollowers(false);
        }
        List<ObjectId> pageIds = new ArrayList<>();
        List<ObjectId> pendingPageIds = new ArrayList<>();
        List<ObjectId> sendedPageIds = new ArrayList<>();

        if (StringUtils.isNotBlank(params.get("pageIds"))) {
            String[] pageIdArray = StringUtils.split(params.get("pageIds"), "|");
            for (String pageId : pageIdArray) {
                if (pageId.startsWith("fake")) {
                    Page fakePage = new Page();
                    fakePage.setUserId(user.getId());
                    fakePage.setOwnerId(user.getId());
                    fakePage.setName(pageId.substring(5));
                    Slugify slg = new Slugify();
                    String identifier = fakePage.getName();
                    Page exist = PageDao.loadPageByIdentifier(slg.slugify(identifier));
                    if (exist != null) {
                        identifier = fakePage.getName() + "-" + RouteUtils.generateIdentifier();
                    }
                    fakePage.setIdentifier(slg.slugify(identifier));
                    fakePage.setIsFake(true);
                    fakePage.setPageTagging("everyone");

                    ObjectId fakePageId = PageDao.insertPage(fakePage);
                    pageIds.add(fakePageId);
                } else {
                    ObjectId realPageId = ParamUtils.toObjectId(pageId);
                    try {
                        Page page = PageDao.loadPage(realPageId);
                        if (page != null && !StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                            // Page requires approval - add to pending list
                            pendingPageIds.add(realPageId);
                        } else {
                            // Page is open or doesn't exist - add to regular list
                            pageIds.add(realPageId);
                        }
                    } catch (Exception ex) {
                        // If page can't be loaded, add to regular list
                        pageIds.add(realPageId);
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(params.get("pendingPageIds"))) {
            String[] pageIdArray = StringUtils.split(params.get("pendingPageIds"), "|");
            for (String pageId : pageIdArray) {
                pendingPageIds.add(ParamUtils.toObjectId(pageId));
            }
        }

        tmpEvent.setPageIds(pageIds);
        tmpEvent.setInitialPageIds(pageIds);        // per container bisogna usare questi altrimenti carica troppe pagine>
        if (!pendingPageIds.isEmpty()) {
            tmpEvent.setPendingPageIds(pendingPageIds);
        } else {
            tmpEvent.setPendingPageIds(null);
        }
        if (!sendedPageIds.isEmpty()) {
            tmpEvent.setSendedPageIds(sendedPageIds);
        } else {
            tmpEvent.setSendedPageIds(null);
        }

        // Send email notifications to page owners for pending approvals
        if (!pendingPageIds.isEmpty()) {
            List<ObjectId> newPendingPages = new ArrayList<>();
            for (ObjectId pageId : pendingPageIds) {
                if (!sendedPageIds.contains(pageId)) {
                    newPendingPages.add(pageId);
                }
            }

            if (!newPendingPages.isEmpty()) {
                String language = RouteUtils.language(request);
                for (ObjectId pageId : newPendingPages) {
                    try {
                        Page page = PageDao.loadPage(pageId);
                        if (page != null && page.getOwnerId() != null) {
                            User pageOwner = UserDao.loadUser(page.getOwnerId());
                            if (pageOwner != null) {
                                boolean emailSent = NotificationCommons.notifyPageApproval(
                                    request, tmpEvent, page, pageOwner, user, language);
                                if (emailSent) {
                                    sendedPageIds.add(pageId);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        LOGGER.error("Error sending page approval email for pageId: " + pageId, ex);
                    }
                }

                // Update sendedPageIds in the event
                if (!sendedPageIds.isEmpty()) {
                    tmpEvent.setSendedPageIds(sendedPageIds);
                } else {
                    tmpEvent.setSendedPageIds(null);
                }
            }
        }
        if (StringUtils.isNotBlank(tmpEvent.getDescription())) {
            tmpEvent.setDescription(StringUtils.trim(tmpEvent.getDescription()));
        }

        if (EventCommons.isValidEvent(tmpEvent)) {
            tmpEvent.setStatus(EventStatusType.published.toString());
            tmpEvent.setUserId(user.getId());
            tmpEvent.setOwnerId(user.getId());
            Slugify slg = new Slugify();

            String identifier = tmpEvent.getName();
            Event exist = EventDao.loadEventByIdentifier(slg.slugify(identifier));
            if (exist != null) {
                identifier = tmpEvent.getName() + "-" + RouteUtils.generateIdentifier();
            }

            tmpEvent.setIdentifier(slg.slugify(identifier));

            Location location = geocode(tmpEvent);
            if (location != null) {
                tmpEvent.setLat(location.getCoordinates()[0].toString());
                tmpEvent.setLng(location.getCoordinates()[1].toString());
            }

            ObjectId eventId = EventDao.insertEvent(tmpEvent);
            tmpEvent.setId(eventId);
            // logo
            String slimlogo = params.get("uploaded-cover");
            if (StringUtils.isNotBlank(slimlogo)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null && uploaded.getBytes() != null)) {
                    EventDao.updateCoverImage(user.getUsername(), eventId, uploaded);
                }
            }

            if (!files.isEmpty()) {
                EventDao.updateEventLocandina(eventId, files);
            }

            if ((tmpEvent.getStartDate() != null && !tmpEvent.getStartDate().before(new Date())) || StringUtils.equalsIgnoreCase(tmpEvent.getType(), "container")) {
                EntityNotificationCommons.notifyEntityEvent(EventNotificationType.published, tmpEvent);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati pagina non validi");
        }
        return "ok";
    };

    public static Route event_edit_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("oid"));

        if (eventId != null) {
            // params
            Event eventOld = EventDao.loadEvent(eventId);
            if (eventOld != null) {
                Event tmpEvent = (Event) BeanUtils.cloneBean(eventOld);

                tmpEvent = PojoUtils.mergeFromParams(params, tmpEvent);
                if (StringUtils.isBlank(params.get("freeEntry"))) {
                    tmpEvent.setFreeEntry(false);
                }
                if (StringUtils.isBlank(params.get("showFollowers"))) {
                    tmpEvent.setShowFollowers(false);
                }
                List<ObjectId> pageIds = new ArrayList<>();
                List<ObjectId> pendingPageIds = new ArrayList<>();
                List<ObjectId> sendedPageIds = tmpEvent.getSendedPageIds() != null ? new ArrayList<>(tmpEvent.getSendedPageIds()) : new ArrayList<>();

                if (StringUtils.isNotBlank(params.get("pageIds"))) {
                    String[] pageIdArray = StringUtils.split(params.get("pageIds"), "|");
                    for (String pageId : pageIdArray) {
                        if (pageId.startsWith("fake")) {
                            Page fakePage = new Page();
                            fakePage.setUserId(user.getId());
                            fakePage.setOwnerId(user.getId());
                            fakePage.setName(pageId.substring(5));
                            Slugify slg = new Slugify();
                            String identifier = fakePage.getName();
                            Page exist = PageDao.loadPageByIdentifier(slg.slugify(identifier));
                            if (exist != null) {
                                identifier = fakePage.getName() + "-" + RouteUtils.generateIdentifier();
                            }
                            fakePage.setIdentifier(slg.slugify(identifier));
                            fakePage.setIsFake(true);
                            fakePage.setPageTagging("everyone");

                            ObjectId fakePageId = PageDao.insertPage(fakePage);
                            pageIds.add(fakePageId);
                        } else {
                            ObjectId realPageId = ParamUtils.toObjectId(pageId);
                            try {
                                Page page = PageDao.loadPage(realPageId);
                                if (page != null && !StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                    // Page requires approval - add to pending list
                                    pendingPageIds.add(realPageId);
                                } else {
                                    // Page is open or doesn't exist - add to regular list
                                    pageIds.add(realPageId);
                                }
                            } catch (Exception ex) {
                                // If page can't be loaded, add to regular list
                                pageIds.add(realPageId);
                            }
                        }
                    }
                }
                if (StringUtils.isNotBlank(params.get("pendingPageIds"))) {
                    String[] pageIdArray = StringUtils.split(params.get("pendingPageIds"), "|");
                    for (String pageId : pageIdArray) {
                        pendingPageIds.add(ParamUtils.toObjectId(pageId));
                    }
                }

                tmpEvent.setPageIds(pageIds);
                if (!pendingPageIds.isEmpty()) {
                    tmpEvent.setPendingPageIds(pendingPageIds);
                } else {
                    tmpEvent.setPendingPageIds(null);
                }
                if (!sendedPageIds.isEmpty()) {
                    tmpEvent.setSendedPageIds(sendedPageIds);
                } else {
                    tmpEvent.setSendedPageIds(null);
                }

                // Send email notifications to page owners for pending approvals
                if (!pendingPageIds.isEmpty()) {
                    List<ObjectId> newPendingPages = new ArrayList<>();
                    for (ObjectId pageId : pendingPageIds) {
                        if (!sendedPageIds.contains(pageId)) {
                            newPendingPages.add(pageId);
                        }
                    }

                    if (!newPendingPages.isEmpty()) {
                        String language = RouteUtils.language(request);
                        for (ObjectId pageId : newPendingPages) {
                            try {
                                Page page = PageDao.loadPage(pageId);
                                if (page != null && page.getOwnerId() != null) {
                                    User pageOwner = UserDao.loadUser(page.getOwnerId());
                                    if (pageOwner != null) {
                                        boolean emailSent = NotificationCommons.notifyPageApproval(
                                            request, tmpEvent, page, pageOwner, user, language);
                                        if (emailSent) {
                                            sendedPageIds.add(pageId);
                                        }
                                    }
                                }
                            } catch (Exception ex) {
                                LOGGER.error("Error sending page approval email for pageId: " + pageId, ex);
                            }
                        }

                        // Update sendedPageIds in the event
                        tmpEvent.setSendedPageIds(sendedPageIds);
                    }
                }
                if (tmpEvent.getParentId() != null && !params.containsKey("parentId")) {
                    // devo prima aggiornare il parent
                    Event parent = EventDao.loadEvent(tmpEvent.getParentId());
                    if (parent != null && parent.getChildIds() != null && !parent.getChildIds().isEmpty()) {
                        parent.getChildIds().remove(tmpEvent.getId());
                        EventDao.updateEvent(parent);
                    }

                    tmpEvent.setParentId(null);
                }
                if (StringUtils.isNotBlank(tmpEvent.getDescription())) {
                    tmpEvent.setDescription(StringUtils.trim(tmpEvent.getDescription()));
                }

                if (EventCommons.isValidEvent(tmpEvent)) {
                    if (StringUtils.isEmpty(tmpEvent.getLat())
                            || !StringUtils.equals(eventOld.getCity(), tmpEvent.getCity())
                            || !StringUtils.equals(eventOld.getAddress(), tmpEvent.getAddress())) {
                        Location location = geocode(tmpEvent);
                        if (location != null) {
                            tmpEvent.setLat(location.getCoordinates()[0].toString());
                            tmpEvent.setLng(location.getCoordinates()[1].toString());
                        }
                    }

                    // tmpEvent.setPublicationDate(tmpEvent.getStartDate() != null ? tmpEvent.getStartDate() : new Date());
                    EventDao.updateEvent(tmpEvent);

                    // image
                    String slimlogo = params.get("uploaded-cover");
                    if (StringUtils.isNotBlank(slimlogo)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if (uploaded != null) {
                            if (uploaded.getBytes() != null) {
                                EventDao.updateCoverImage(user.getUsername(), eventId, uploaded);
                            }
                        } else {
                            EventDao.removeCoverImage(eventId);
                        }
                    } else {
                        EventDao.removeCoverImage(eventId);
                    }

                    if ((tmpEvent.getStartDate() != null && !tmpEvent.getStartDate().before(new Date())) || StringUtils.equalsIgnoreCase(tmpEvent.getType(), "container")) {
                        if (needNotifyChangeEvent(eventOld, tmpEvent)) {
                            EntityNotificationCommons.notifyEntityEvent(EventNotificationType.changed, tmpEvent);
                        }
                    }

                    if (!files.isEmpty()) {
                        EventDao.updateEventLocandina(eventId, files);
                    }
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati evento non validi");
                }
                return tmpEvent.getIdentifier();
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati evento non validi");
            }
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route event_edit_file_remove = (Request request, Response response) -> {

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("oid"));

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        if (eventId != null) {
            // params
            Event eventToUpdate = EventDao.loadEvent(eventId);
            if (eventToUpdate != null) {
                eventToUpdate.setLocandina(null);
                EventDao.updateEvent(eventToUpdate);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };

    public static Route event_report_send = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        ObjectId eventId = ParamUtils.toObjectId(params.get("eventId"));

        if (eventId != null) {
            EventReport eventReport = PojoUtils.createFromParams(params, EventReport.class);
            eventReport.setUserId(user.getId());
            EventReportDao.insertEventReport(eventReport);
            return "ok";

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route event_remove = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null)
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("eventId"));
        if (eventId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Event event = null;
        try {
            event = EventDao.loadEvent(eventId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (event == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        if (!event.getOwnerId().equals(user.getId())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        EventDao.updateEventCancelled(eventId, true);

        EventFollowerDao.updateEventFollowerListCancelledByEventId(eventId, true);
        if ((event.getStartDate() != null && !event.getStartDate().before(new Date())) || StringUtils.equalsIgnoreCase(event.getType(), "container")) {
            EntityNotificationCommons.notifyEntityEvent(EventNotificationType.deleted, event);
        }

        return "ok";
    };

    public static Route event_disconnect_children = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // only system and admin users can disconnect children
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // get event id
        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("oid"));
        if (eventId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "empty eventId");
        }

        // load parent event
        Event parentEvent = null;
        try {
            parentEvent = EventDao.loadEvent(eventId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (parentEvent == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "event not found");
        }

        // verify it's a container event
        if (!StringUtils.equalsIgnoreCase(parentEvent.getType(), "container")) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "event is not a container");
        }

        try {
            // load all child events
            List<Event> childEvents = EventDao.loadEventListByParentId(eventId);

            if (childEvents != null && !childEvents.isEmpty()) {
                // disconnect all child events by setting parentId to null
                for (Event childEvent : childEvents) {
                    childEvent.setParentId(null);
                    EventDao.updateEvent(childEvent, true); // skip parent check to avoid recursion
                }

                // clear parent's childIds array
                parentEvent.setChildIds(new ArrayList<>());
                parentEvent.setPageIds(new ArrayList<>());
                parentEvent.setPendingPageIds(new ArrayList<>());
                EventDao.updateEvent(parentEvent, true); // skip parent check
            }

        } catch (Exception ex) {
            LOGGER.error("Error disconnecting child events", ex);
            throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, "database error");
        }

        return "ok";
    };

    public static Route event_page_approval = (Request request, Response response) -> {
        // Parameters from URL query string
        String registrationToken = request.queryParams("registrationToken");
        String eventIdStr = request.queryParams("eventId");
        String pageIdStr = request.queryParams("pageId");
        String action = request.queryParams("action"); // "accept" or "decline"

        // Validate required parameters
        if (StringUtils.isBlank(registrationToken) || StringUtils.isBlank(eventIdStr) ||
            StringUtils.isBlank(pageIdStr) || StringUtils.isBlank(action)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Missing required parameters");
        }

        // Validate action parameter
        if (!StringUtils.equalsIgnoreCase(action, "accept") && !StringUtils.equalsIgnoreCase(action, "decline")) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Invalid action parameter");
        }

        try {
            // Authenticate user via registrationToken
            User user = UserDao.loadUserByRegistrationToken(registrationToken);
            if (user == null) {
                throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "Invalid registration token");
            }

            // Parse IDs
            ObjectId eventId = ParamUtils.toObjectId(eventIdStr);
            ObjectId pageId = ParamUtils.toObjectId(pageIdStr);

            if (eventId == null || pageId == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Invalid event or page ID");
            }

            // Load event and page
            Event event = EventDao.loadEvent(eventId);
            Page page = PageDao.loadPage(pageId);

            if (event == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, "Event not found");
            }

            if (page == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, "Page not found");
            }

            // Verify user is the page owner
            if (!Objects.equals(page.getOwnerId(), user.getId())) {
                throw Spark.halt(HttpStatus.FORBIDDEN_403, "User is not the page owner");
            }

            // Verify pageId is in pendingPageIds
            if (event.getPendingPageIds() == null || !event.getPendingPageIds().contains(pageId)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Page is not pending approval for this event");
            }

            // Process the action
            List<ObjectId> pendingPageIds = new ArrayList<>(event.getPendingPageIds());
            List<ObjectId> pageIds = event.getPageIds() != null ? new ArrayList<>(event.getPageIds()) : new ArrayList<>();

            if (StringUtils.equalsIgnoreCase(action, "accept")) {
                // Move pageId from pendingPageIds to pageIds
                pendingPageIds.remove(pageId);
                pageIds.add(pageId);
            } else {
                // Remove pageId from pendingPageIds (decline)
                pendingPageIds.remove(pageId);
            }

            // Update event
            event.setPendingPageIds(pendingPageIds);
            event.setPageIds(pageIds);
            EventDao.updateEvent(event);

            // For container, need to update the pageIds too
            if (event.getParentId() != null) {
                Event parent = EventDao.loadEvent(event.getParentId());
                if (parent != null) {
                    // Move the pageId from pendingPageIds to pageIds
                    if (parent.getPendingPageIds() != null) {
                        parent.getPendingPageIds().remove(pageId);
                    }
                    if (!parent.getPageIds().contains(pageId)) {
                        parent.getPageIds().add(pageId);
                    }
                    EventDao.updateEvent(parent);
                }
            }

            // Redirect to result page with success message
            String language = RouteUtils.language(request);
            String resultUrl = RouteUtils.language(RouteUtils.baseUrl(request) + Paths.EVENT_PAGE_APPROVAL_RESULT, language)
                    + "?action=" + action
                    + "&eventId=" + eventIdStr
                    + "&pageId=" + pageIdStr;

            response.redirect(resultUrl);
            return null;

        } catch (Exception ex) {
            LOGGER.error("Error processing page approval", ex);
            throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, "Internal server error");
        }
    };

    public static TemplateViewRoute event_page_approval_result = (Request request, Response response) -> {
        // Parameters from URL query string
        String action = request.queryParams("action");
        String eventIdStr = request.queryParams("eventId");
        String pageIdStr = request.queryParams("pageId");

        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);
        attributes.put("action", action);

        // Load event and page details if available
        if (StringUtils.isNotBlank(eventIdStr) && StringUtils.isNotBlank(pageIdStr)) {
            try {
                ObjectId eventId = ParamUtils.toObjectId(eventIdStr);
                ObjectId pageId = ParamUtils.toObjectId(pageIdStr);

                if (eventId != null && pageId != null) {
                    Event event = EventDao.loadEvent(eventId);
                    Page page = PageDao.loadPage(pageId);

                    attributes.put("event", event);
                    attributes.put("page", page);
                }
            } catch (Exception ex) {
                LOGGER.error("Error loading event/page details for result page", ex);
            }
        }

        return Manager.render(Templates.EVENT_PAGE_APPROVAL_RESULT, attributes, RouteUtils.pathType(request));
    };

    public static Route event_follower_toggle = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null)
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("eventId"));
        if (eventId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Event event = null;
        try {
            event = EventDao.loadEvent(eventId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (event == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product wishlist
        EventFollower eventFollower = null;
        try {
            eventFollower = EventFollowerDao.loadEventFollowerByUserAndEvent(user.getId(), eventId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        if (eventFollower == null) {

            // add to wishlist
            eventFollower = new EventFollower();
            eventFollower.setUserId(user.getId());
            eventFollower.setEventId(event.getId());

            EventFollowerDao.insertEventFollower(eventFollower);

        } else {

            // remove from wishlist
            EventFollowerDao.updateEventFollowerCancelled(eventFollower.getId(), true);

        }

        return "ok";
    };

    public static Route event_notification_toggle = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null)
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("eventId"));
        if (eventId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Event event = null;
        try {
            event = EventDao.loadEvent(eventId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (event == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product wishlist
        EventNotification eventNotification = null;
        try {
            eventNotification = EventNotificationDao.loadEventNotificationByUserAndEvent(user.getId(), eventId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        if (eventNotification == null) {

            // add to wishlist
            eventNotification = new EventNotification();
            eventNotification.setUserId(user.getId());
            eventNotification.setEventId(event.getId());

            EventNotificationDao.insertEventNotification(eventNotification);

        } else {

            // remove from wishlist
            EventNotificationDao.updateEventNotificationCancelled(eventNotification.getId(), true);

        }

        return "ok";
    };

    public static TemplateViewRoute event = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // identifier
        String qrcode = request.queryParams("qrcode");
        attributes.put("qrcode", qrcode);

        // post
        Event event = null;
        if (StringUtils.isNotBlank(qrcode)) {
            event = EventDao.loadEventByQrCode(qrcode);
        }

        response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.EVENT_BASE + "/" + event.getIdentifier() + "?qrcode=" + qrcode, Defaults.LANGUAGE));

        return null;

    };

    public static TemplateViewRoute event_detail = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        }

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        // event
        Event event = null;
        if (StringUtils.isNotBlank(identifier)) {
            event = EventDao.loadEventByIdentifier(identifier);
        }

        // if event is null redirect to home
        if (event == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // localized versions of current path
        // rewrite paths
        if (identifier != null) {
            Map<String, String> paths = Paths.localizedPathsMap(Paths.EVENT_BASE);
            for (Map.Entry<String, String> entry : paths.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String updatedValue = value + "/" + identifier;
                paths.put(key, updatedValue);
            }
            attributes.put("paths", paths);
        } else {
            // localized versions of current path
            attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));
        }

        attributes.put("entry", EventCommons.toEntry(event, user != null ? user.getId() : null));
        if (event != null) {
            attributes.put("isContainer", StringUtils.equalsIgnoreCase(event.getType(), "container"));
        }

        return Manager.render(Templates.EVENT_DETAIL, attributes, RouteUtils.pathType(request));
    };

    public static Route event_list_ajax = (Request request, Response response) -> {
        // Set content type for JSON response
        response.type("application/json");

        // Get parameters
        String eventIdStr = request.queryParams("eventId");
        String pageIdStr = request.queryParams("pageId");
        String sortOrder = request.queryParams("sortOrder");
        String skipStr = request.queryParams("skip");
        String limitStr = request.queryParams("limit");
        int skip = 0;
        int limit = 999; // Default to maximum
        if (StringUtils.isNotBlank(skipStr)) {
            skip = NumberUtils.toInt(skipStr);
        }
        if (StringUtils.isNotBlank(limitStr)) {
            limit = NumberUtils.toInt(limitStr);
        }

        // Validate eventId parameter
        ObjectId eventId = null;
        if (StringUtils.isNotBlank(eventIdStr)) {
            try {
                eventId = new ObjectId(eventIdStr);
            } catch (IllegalArgumentException e) {
                response.status(HttpStatus.BAD_REQUEST_400);
                return "{\"error\": \"Invalid eventId format\"}";
            }
        }

        // Validate pageId parameter
        ObjectId pageId = null;
        if (StringUtils.isNotBlank(pageIdStr)) {
            try {
                pageId = new ObjectId(pageIdStr);
            } catch (IllegalArgumentException e) {
                response.status(HttpStatus.BAD_REQUEST_400);
                return "{\"error\": \"Invalid pageId format\"}";
            }
        }

        try {
            // Load events with filtering and sorting
            List<Event> events;
            if (pageId != null) {
                events = EventDao.loadEventListFromContainerByPageId(eventId, pageId, skip, limit, sortOrder);
            } else {
                // If no pageId specified, return all the events in the container
                events = EventDao.loadEventListByParentId(eventId);
            }

            // Convert to entries for frontend consumption
            List<EventEntry> eventEntries = EventCommons.toEntries(events);

            // Build JSON response
            JsonObject json = new JsonObject();
            JsonArray eventsArray = new JsonArray();

            if (eventEntries != null && !eventEntries.isEmpty()) {
                for (EventEntry entry : eventEntries) {
                    JsonObject eventObj = new JsonObject();
                    Event event = entry.getEvent();

                    // Basic event information
                    eventObj.addProperty("id", event.getId().toString());
                    eventObj.addProperty("name", event.getName());
                    eventObj.addProperty("identifier", event.getIdentifier());
                    eventObj.addProperty("description", event.getDescription());

                    // Dates
                    if (event.getStartDate() != null) {
                        eventObj.addProperty("startDate", event.getStartDate().getTime());
                    }
                    if (event.getEndDate() != null) {
                        eventObj.addProperty("endDate", event.getEndDate().getTime());
                    }
                    if (event.getCreation() != null) {
                        eventObj.addProperty("creation", event.getCreation().getTime());
                    }

                    // Time information
                    eventObj.addProperty("startHour", event.getStartHour());
                    eventObj.addProperty("endHour", event.getEndHour());

                    // Location
                    eventObj.addProperty("fulladdress", event.getFulladdress());
                    eventObj.addProperty("city", event.getCity());

                    // Images
                    if (event.getCoverImageId() != null) {
                        eventObj.addProperty("coverImageId", event.getCoverImageId().toString());
                    }

                    // Additional info
                    eventObj.addProperty("freeEntry", event.getFreeEntry() != null ? event.getFreeEntry() : false);
                    if (event.getTicketsMinPrice() != null) {
                        eventObj.addProperty("ticketsMinPrice", event.getTicketsMinPrice());
                    }

                    // Follower count
                    eventObj.addProperty("followerCount", entry.getFollowerCount());

                    eventsArray.add(eventObj);
                }
            }

            json.add("events", eventsArray);
            json.addProperty("success", true);
            json.addProperty("count", eventsArray.size());

            return json.toString();

        } catch (Exception ex) {
            LOGGER.error("Error loading events for AJAX request", ex);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "{\"error\": \"Internal server error\", \"success\": false}";
        }
    };

    public static Route event_pages_ajax = (Request request, Response response) -> {
        // Set content type for JSON response
        response.type("application/json");

        // Get parameters
        String eventIdStr = request.queryParams("eventId");

        // Validate eventId parameter
        ObjectId eventId = null;
        if (StringUtils.isNotBlank(eventIdStr)) {
            try {
                eventId = new ObjectId(eventIdStr);
            } catch (IllegalArgumentException e) {
                response.status(HttpStatus.BAD_REQUEST_400);
                return "{\"error\": \"Invalid eventId format\"}";
            }
        }

        try {
            List<Page> pages = new ArrayList<>();

            if (eventId != null) {
                // Load the event to get its pageIds
                Event event = EventDao.loadEvent(eventId);
                if (event != null && event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                    // Load all pages associated with this event
                    pages = PageDao.loadPages(event.getPageIds());
                }
            }

            // Build JSON response
            JsonObject json = new JsonObject();
            JsonArray pagesArray = new JsonArray();

            if (pages != null && !pages.isEmpty()) {
                for (Page page : pages) {
                    JsonObject pageObj = new JsonObject();

                    // Basic page information
                    pageObj.addProperty("id", page.getId().toString());
                    pageObj.addProperty("name", page.getName());
                    pageObj.addProperty("identifier", page.getIdentifier());

                    // Profile image
                    if (page.getProfileImageId() != null) {
                        pageObj.addProperty("profileImageId", page.getProfileImageId().toString());
                    }

                    // Additional info that might be useful
                    pageObj.addProperty("description", page.getDescription());
                    pageObj.addProperty("shortDescription", page.getShortDescription());

                    pagesArray.add(pageObj);
                }
            }

            json.add("pages", pagesArray);
            json.addProperty("success", true);
            json.addProperty("count", pagesArray.size());

            return json.toString();

        } catch (Exception ex) {
            LOGGER.error("Error loading pages for AJAX request", ex);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "{\"error\": \"Internal server error\", \"success\": false}";
        }
    };

    public static Route event_navigation_ajax = (Request request, Response response) -> {
        // Set content type for JSON response
        response.type("application/json");

        // Get parameters
        String parentIdStr = request.queryParams("parentId");
        String currentEventIdStr = request.queryParams("currentEventId");

        // Validate parameters
        ObjectId parentId = null;
        ObjectId currentEventId = null;

        if (StringUtils.isNotBlank(parentIdStr)) {
            try {
                parentId = new ObjectId(parentIdStr);
            } catch (IllegalArgumentException e) {
                response.status(HttpStatus.BAD_REQUEST_400);
                return "{\"error\": \"Invalid parentId format\"}";
            }
        }

        if (StringUtils.isNotBlank(currentEventIdStr)) {
            try {
                currentEventId = new ObjectId(currentEventIdStr);
            } catch (IllegalArgumentException e) {
                response.status(HttpStatus.BAD_REQUEST_400);
                return "{\"error\": \"Invalid currentEventId format\"}";
            }
        }

        if (parentId == null || currentEventId == null) {
            response.status(HttpStatus.BAD_REQUEST_400);
            return "{\"error\": \"Missing parentId or currentEventId parameter\"}";
        }

        try {
            // Load all child events for the parent, sorted by date
            List<Event> childEvents = EventDao.loadEventListByParentId(parentId);

            if (childEvents == null || childEvents.isEmpty()) {
                response.status(HttpStatus.NOT_FOUND_404);
                return "{\"error\": \"No child events found\"}";
            }

            // Sort events by start date (ascending)
            childEvents.sort((e1, e2) -> {
                if (e1.getStartDate() == null && e2.getStartDate() == null) return 0;
                if (e1.getStartDate() == null) return 1;
                if (e2.getStartDate() == null) return -1;
                return e1.getStartDate().compareTo(e2.getStartDate());
            });

            // Find current event index
            int currentIndex = -1;
            for (int i = 0; i < childEvents.size(); i++) {
                if (childEvents.get(i).getId().equals(currentEventId)) {
                    currentIndex = i;
                    break;
                }
            }

            if (currentIndex == -1) {
                response.status(HttpStatus.NOT_FOUND_404);
                return "{\"error\": \"Current event not found in parent's children\"}";
            }

            // Build JSON response
            JsonObject json = new JsonObject();
            json.addProperty("success", true);

            // Previous event
            if (currentIndex > 0) {
                Event prevEvent = childEvents.get(currentIndex - 1);
                JsonObject prevObj = new JsonObject();
                prevObj.addProperty("id", prevEvent.getId().toString());
                prevObj.addProperty("identifier", prevEvent.getIdentifier());
                prevObj.addProperty("name", prevEvent.getName());
                json.add("previous", prevObj);
            } else {
                json.add("previous", null);
            }

            // Next event
            if (currentIndex < childEvents.size() - 1) {
                Event nextEvent = childEvents.get(currentIndex + 1);
                JsonObject nextObj = new JsonObject();
                nextObj.addProperty("id", nextEvent.getId().toString());
                nextObj.addProperty("identifier", nextEvent.getIdentifier());
                nextObj.addProperty("name", nextEvent.getName());
                json.add("next", nextObj);
            } else {
                json.add("next", null);
            }

            return json.toString();

        } catch (Exception ex) {
            LOGGER.error("Error loading navigation for AJAX request", ex);
            response.status(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return "{\"error\": \"Internal server error\", \"success\": false}";
        }
    };

    public static TemplateViewRoute be_events = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.eventManager.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        String language = StringUtils.defaultIfBlank(request.queryParams("language"), Defaults.LANGUAGE);
        attributes.put("language", language);

        // date filter
        Date endDate = TimeUtils.today();
        Date startDate = DateUtils.addDays(new Date(), -60);

        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }

        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);

        String[] selectedProvinces = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedProvinces"))) {
            selectedProvinces = StringUtils.split(request.queryParams("selectedProvinces"), "|");
        }
        attributes.put("selectedProvinces", selectedProvinces);

//        List<Event> eventListComplete = EventDao.loadEventListByDateRange(startDate, endDate);
        List<ProvinceEntry> provinceList;
        provinceList = EventDao.loadEventProvinceList();
        attributes.put("provinceList", provinceList);

//        List<Event> eventList = null;
//        if ((eventListComplete != null)
//                && (eventListComplete.size() > 0)) {
//
//            for (Event event : eventListComplete) {
//                boolean add = true;
//                if (selectedProvinces != null) {
//                    if (event.getProvinceCode() != null) {
//                        if (!Arrays.stream(selectedProvinces).anyMatch(event.getProvinceCode()::equals)) {
//                            add = false;
//                        }
//                    } else {
//                        add = false;
//                    }
//                }
//                if (add) {
//                    if (eventList == null) {
//                        eventList = new ArrayList<>();
//                    }
//
//                    eventList.add(event);
//                }
//
//            }
//        }
//        attributes.put("eventList", EventCommons.toEntries(eventList));
        return Manager.render(Templates.BE_EVENTS, attributes, RouteUtils.pathType(request));
    };

    public static Route be_events_data = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.eventManager.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        String language = StringUtils.defaultIfBlank(request.queryParams("language"), Defaults.LANGUAGE);
        attributes.put("language", language);

        // date filter
        Date endDate = TimeUtils.today();
        Date startDate = DateUtils.addDays(new Date(), -60);

        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }

        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);

        String[] selectedProvinces = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedProvinces"))) {
            selectedProvinces = StringUtils.split(request.queryParams("selectedProvinces"), "|");
        }
        attributes.put("selectedProvinces", selectedProvinces);

        List<Event> eventListComplete = EventDao.loadEventListByDateRange(startDate, endDate);
        List<Event> eventList = null;
        if (eventListComplete != null && !eventListComplete.isEmpty()) {

            for (Event entry : eventListComplete) {
                boolean add = true;
                if (selectedProvinces != null) {
                    if (entry.getProvinceCode() != null) {
                        if (!Arrays.stream(selectedProvinces).anyMatch(entry.getProvinceCode()::equals)) {
                            add = false;
                        }
                    } else {
                        add = false;
                    }
                }
                if (add) {
                    if (eventList == null) {
                        eventList = new ArrayList<>();
                    }

                    eventList.add(entry);
                }

            }
        }
        List<EventEntry> eventEntryList = EventCommons.toEntries(eventList, null, Arrays.asList("customer"));

        String context = RouteUtils.contextPath(request);

        JsonObject json = new JsonObject();
        if (eventEntryList != null && !eventEntryList.isEmpty()) {
            JsonArray array = new JsonArray();
            for (EventEntry entry : eventEntryList) {
                JsonArray element = new JsonArray();

                // 1
                String img = "n.d.";
                if (entry.getEvent().getCoverImageId() != null) {
                    img = "<img src='" + context + Paths.IMAGE_SYSTEM + "?oid=" + entry.getEvent().getCoverImageId() + "' width='120' loading='lazy'/>";
                }
                element.add(new JsonPrimitive(img));

                // 2
                String customer = "Agorapp";
                if (entry.getCustomer() != null) {
                    customer = entry.getCustomer().getName() + " " + entry.getCustomer().getLastname();
                }
                element.add(new JsonPrimitive(customer));

                // 3
                String pageName = "<div id='eventId' style='display: none' sortable-value='" + entry.getEvent().getName() + "'>" + entry.getEvent().getId() + "</div>";
                pageName += "<a href='" + context + Paths.BE_EVENT_EDIT + "?oid=" + entry.getEvent().getId() + "' class='text-bold' target='_blank' sortable-value='" + entry.getEvent().getName() + "'>" + entry.getEvent().getName() + "</a>";
                element.add(new JsonPrimitive(pageName));

                // 4
                String description = entry.getEvent() != null ? entry.getEvent().getDescription() : "";
                String truncatedDescription = description != null ?
                        (description.length() > 100 ? description.substring(0, 100) + "..." : description) : "";
                element.add(new JsonPrimitive(truncatedDescription));

                // 5
                String link = "<a href='" + context + Paths.EVENT_BASE.replace(":language", language) + "/" + entry.getEvent().getIdentifier() + "' target='_blank'>Link ↗</a>";
                element.add(new JsonPrimitive(link));

                // 6
                String identificativo = entry.getEvent().getIdentifier();
                if (StringUtils.isNotBlank(identificativo)) {
                    element.add(new JsonPrimitive(identificativo));
                } else {
                    element.add(new JsonPrimitive(""));
                }

                // 7
                if (StringUtils.isNotBlank(entry.getEvent().getFulladdress())) {
                    element.add(new JsonPrimitive(entry.getEvent().getFulladdress()));
                } else {
                    element.add(new JsonPrimitive(""));
                }

                SimpleDateFormat dateFormatWithTime = new SimpleDateFormat("dd MMM yyyy HH:mm", new Locale("it", "IT"));
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMM yyyy", new Locale("it", "IT"));
                SimpleDateFormat dateFormatSort = new SimpleDateFormat("yyyyMMdd");
                SimpleDateFormat dateFormatSortWithTime = new SimpleDateFormat("yyyyMMddHHmm");
                // 8
                if (entry.getEvent().getStartDate() != null) {
                    String startDateEvent = "<span sortable-value='" + dateFormatSort.format(entry.getEvent().getStartDate()) + StringUtils.defaultIfBlank(entry.getEvent().getStartHour(), "") + "'>" + dateFormat.format(entry.getEvent().getStartDate()) + " " + StringUtils.defaultIfBlank(entry.getEvent().getStartHour(), "") + "</span>";
                    element.add(new JsonPrimitive(startDateEvent));
                } else {
                    element.add(new JsonPrimitive(""));
                }

                // 9
                if (entry.getEvent().getEndDate() != null) {
                    String endDateEvent = "<span sortable-value='" + dateFormatSort.format(entry.getEvent().getEndDate()) + StringUtils.defaultIfBlank(entry.getEvent().getEndHour(), "") + "'>" + dateFormat.format(entry.getEvent().getEndDate()) + " " + StringUtils.defaultIfBlank(entry.getEvent().getEndHour(), "") + "</span>";
                    element.add(new JsonPrimitive(endDateEvent));
                } else {
                    element.add(new JsonPrimitive(""));
                }

                // 10
                String creation = "<span sortable-value='" + dateFormatSortWithTime.format(entry.getEvent().getCreation()) + "'>" + dateFormatWithTime.format(entry.getEvent().getCreation()) + "</span>";
                element.add(new JsonPrimitive(creation));

                // 11
                String actions = "<ul class='icons-list'>\n"
                        + "<li class='dropdown'>\n"
                        + "    <a href='#' class='dropdown-toggle' data-toggle='dropdown'>\n"
                        + "        <i class='icon-menu9'></i>\n"
                        + "    </a>\n"
                        + "    <ul class='dropdown-menu dropdown-menu-right'>\n"
                        + "        <li>\n"
                        + "            <a href='" + context + Paths.BE_EVENT_REMOVE_POSTER + "?oid=" + entry.getEvent().getId() + "'><i class='icon-trash-alt'></i> Elimina copertina</a>\n"
                        + "        </li>\n"
                        + "        <li>\n"
                        + "            <a href='" + context + Paths.BE_EVENT_REMOVE + "?eventId=" + entry.getEvent().getId() + "' class='remove-event'><i class='icon-trash-alt'></i> Elimina evento</a>\n"
                        + "        </li>\n"
                        + "    </ul>\n"
                        + "</li>\n"
                        + "</ul>";
                element.add(new JsonPrimitive(actions));

                // 10
                element.add(new JsonPrimitive(""));

                array.add(element);
            }

            json.add("data", array);
        }

        return json.toString();

    };

    public static TemplateViewRoute be_event_edit = (Request request, Response response) -> {

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        Event event;
        if (oid != null) {
            event = EventDao.loadEvent(oid);
        } else {
            event = new Event();
        }
        attributes.put("event", event);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.BE_EVENT_EDIT, attributes, RouteUtils.pathType(request));
    };

    public static Route be_event_edit_save = (Request request, Response response) -> {
        // params

        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        ObjectId oid = ParamUtils.toObjectId(params.get("oid"));

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        if (oid != null) {
            // params
            Event previous = EventDao.loadEvent(oid);
            String titleOld = previous.getName();
            Event tmpEvent = (Event) BeanUtils.cloneBean(previous);

            tmpEvent = PojoUtils.mergeFromParams(params, tmpEvent);
            if (StringUtils.isBlank(params.get("freeEntry"))) {
                tmpEvent.setFreeEntry(false);
            }
            if (StringUtils.isBlank(params.get("showFollowers"))) {
                tmpEvent.setShowFollowers(false);
            }
            List<ObjectId> pageIds = new ArrayList<>();
            List<ObjectId> pendingPageIds = new ArrayList<>();
            List<ObjectId> sendedPageIds = tmpEvent.getSendedPageIds() != null ? new ArrayList<>(tmpEvent.getSendedPageIds()) : new ArrayList<>();

            if (StringUtils.isNotBlank(params.get("pageIds"))) {
                String[] pageIdArray = StringUtils.split(params.get("pageIds"), "|");
                for (String pageId : pageIdArray) {
                    if (pageId.startsWith("fake")) {
                        Page fakePage = new Page();
                        fakePage.setUserId(user.getId());
                        fakePage.setOwnerId(user.getId());
                        fakePage.setName(pageId.substring(5));
                        Slugify slg = new Slugify();
                        String identifier = fakePage.getName();
                        Page exist = PageDao.loadPageByIdentifier(slg.slugify(identifier));
                        if (exist != null) {
                            identifier = fakePage.getName() + "-" + RouteUtils.generateIdentifier();
                        }
                        fakePage.setIdentifier(slg.slugify(identifier));
                        fakePage.setIsFake(true);

                        ObjectId fakePageId = PageDao.insertPage(fakePage);
                        pageIds.add(fakePageId);
                    } else {
                        ObjectId realPageId = ParamUtils.toObjectId(pageId);
                        try {
                            Page page = PageDao.loadPage(realPageId);
                            if (page != null && !StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                // Page requires approval - add to pending list
                                pendingPageIds.add(realPageId);
                            } else {
                                // Page is open or doesn't exist - add to regular list
                                pageIds.add(realPageId);
                            }
                        } catch (Exception ex) {
                            // If page can't be loaded, add to regular list
                            pageIds.add(realPageId);
                        }
                    }
                }
            }

            tmpEvent.setPageIds(pageIds);
            if (!pendingPageIds.isEmpty()) {
                tmpEvent.setPendingPageIds(pendingPageIds);
            } else {
                tmpEvent.setPendingPageIds(null);
            }
            if (!sendedPageIds.isEmpty()) {
                tmpEvent.setSendedPageIds(sendedPageIds);
            } else {
                tmpEvent.setSendedPageIds(null);
            }
            if (StringUtils.isNotBlank(tmpEvent.getDescription())) {
                tmpEvent.setDescription(StringUtils.trim(tmpEvent.getDescription()));
            }

            // Send email notifications to page owners for pending approvals
            if (!pendingPageIds.isEmpty()) {
                List<ObjectId> newPendingPages = new ArrayList<>();
                for (ObjectId pageId : pendingPageIds) {
                    if (!sendedPageIds.contains(pageId)) {
                        newPendingPages.add(pageId);
                    }
                }

                if (!newPendingPages.isEmpty()) {
                    String language = Defaults.LANGUAGE; // Backend uses default language
                    for (ObjectId pageId : newPendingPages) {
                        try {
                            Page page = PageDao.loadPage(pageId);
                            if (page != null && page.getOwnerId() != null) {
                                User pageOwner = UserDao.loadUser(page.getOwnerId());
                                if (pageOwner != null) {
                                    boolean emailSent = NotificationCommons.notifyPageApproval(
                                        request, tmpEvent, page, pageOwner, user, language);
                                    if (emailSent) {
                                        sendedPageIds.add(pageId);
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            LOGGER.error("Error sending page approval email for pageId: " + pageId, ex);
                        }
                    }

                    // Update sendedPageIds in the event
                    tmpEvent.setSendedPageIds(sendedPageIds);
                }
            }

            if (EventCommons.isValidEvent(tmpEvent)) {
//                if (!StringUtils.equalsIgnoreCase(titleOld, event.getName())) {
//                    Slugify slg = new Slugify();
//                    String identifier = event.getName() + "-" + RouteUtils.generateIdentifier();
//                    event.setIdentifier(slg.slugify(identifier));
//                }
                EventDao.updateEvent(tmpEvent);

                // image
                String slimlogo = params.get("uploaded-cover");
                if (StringUtils.isNotBlank(slimlogo)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        if (uploaded.getBytes() != null) {
                            EventDao.updateCoverImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        EventDao.removeCoverImage(oid);
                    }
                }

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.BE_EVENTS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_EVENT_EDIT) + "?oid=" + tmpEvent.getId());
            }
        } else {
            // params
            Event event = PojoUtils.createFromParams(params, Event.class);

            List<ObjectId> pageIds = new ArrayList<>();
            List<ObjectId> pendingPageIds = new ArrayList<>();
            List<ObjectId> sendedPageIds = new ArrayList<>();

            if (StringUtils.isNotBlank(params.get("pageIds"))) {
                String[] pageIdArray = StringUtils.split(params.get("pageIds"), "|");
                for (String pageId : pageIdArray) {
                    if (pageId.startsWith("fake")) {
                        Page fakePage = new Page();
                        fakePage.setUserId(user.getId());
                        fakePage.setOwnerId(user.getId());
                        fakePage.setName(pageId.substring(5));
                        Slugify slg = new Slugify();
                        String identifier = fakePage.getName();
                        Page exist = PageDao.loadPageByIdentifier(slg.slugify(identifier));
                        if (exist != null) {
                            identifier = fakePage.getName() + "-" + RouteUtils.generateIdentifier();
                        }
                        fakePage.setIdentifier(slg.slugify(identifier));
                        fakePage.setIsFake(true);

                        ObjectId fakePageId = PageDao.insertPage(fakePage);
                        pageIds.add(fakePageId);
                    } else {
                        ObjectId realPageId = ParamUtils.toObjectId(pageId);
                        try {
                            Page page = PageDao.loadPage(realPageId);
                            if (page != null && !StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                // Page requires approval - add to pending list
                                pendingPageIds.add(realPageId);
                            } else {
                                // Page is open or doesn't exist - add to regular list
                                pageIds.add(realPageId);
                            }
                        } catch (Exception ex) {
                            // If page can't be loaded, add to regular list
                            pageIds.add(realPageId);
                        }
                    }
                }
            }

            event.setPageIds(pageIds);
            if (!pendingPageIds.isEmpty()) {
                event.setPendingPageIds(pendingPageIds);
            } else {
                event.setPendingPageIds(null);
            }
            if (!sendedPageIds.isEmpty()) {
                event.setSendedPageIds(sendedPageIds);
            } else {
                event.setSendedPageIds(null);
            }
            if (StringUtils.isNotBlank(event.getDescription())) {
                event.setDescription(StringUtils.trim(event.getDescription()));
            }

            // Send email notifications to page owners for pending approvals
            if (!pendingPageIds.isEmpty()) {
                List<ObjectId> newPendingPages = new ArrayList<>();
                for (ObjectId pageId : pendingPageIds) {
                    if (!sendedPageIds.contains(pageId)) {
                        newPendingPages.add(pageId);
                    }
                }

                if (!newPendingPages.isEmpty()) {
                    String language = Defaults.LANGUAGE; // Backend uses default language
                    for (ObjectId pageId : newPendingPages) {
                        try {
                            Page page = PageDao.loadPage(pageId);
                            if (page != null && page.getOwnerId() != null) {
                                User pageOwner = UserDao.loadUser(page.getOwnerId());
                                if (pageOwner != null) {
                                    boolean emailSent = NotificationCommons.notifyPageApproval(
                                        request, event, page, pageOwner, user, language);
                                    if (emailSent) {
                                        sendedPageIds.add(pageId);
                                    }
                                }
                            }
                        } catch (Exception ex) {
                            LOGGER.error("Error sending page approval email for pageId: " + pageId, ex);
                        }
                    }

                    // Update sendedPageIds in the event
                    event.setSendedPageIds(sendedPageIds);
                }
            }

            if (EventCommons.isValidEvent(event)) {
                event.setUserId(user.getId());
                Slugify slg = new Slugify();
                String identifier = event.getName() + "-" + RouteUtils.generateIdentifier();
                event.setIdentifier(slg.slugify(identifier));

                ObjectId eventId = EventDao.insertEvent(event);

                // image
                String slimlogo = params.get("uploaded-cover");
                if (StringUtils.isNotBlank(slimlogo)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        if (uploaded.getBytes() != null) {
                            EventDao.updateCoverImage(user.getUsername(), eventId, uploaded);
                        }
                    } else {
                        EventDao.removeCoverImage(eventId);
                    }
                }

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.BE_EVENTS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_EVENT_EDIT));
            }
        }

        return null;
    };

    public static Route be_event_remove = (Request request, Response response) -> {

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("eventId"));

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }

        if (eventId != null) {
            // params
            Event event = EventDao.loadEvent(eventId);
            if (event != null) {
                EventDao.updateEventCancelled(eventId, true);
                EventNotificationDao.updateEventNotificationListCancelledByEventId(eventId, true);
//                if (StringUtils.isNotBlank(event.getProtocol())) {
//                    if (!NotificationCommons.notifyEvent(request, NotificationCommons.EventNotificationType.deleted, event)) {
//                        // ...
//                    }
//                }
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };

    public static Route be_event_remove_multiple = (Request request, Response response) -> {

        String eventIdsString = null;
        // posted values (multipart fields parsing)
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "ids":
                        eventIdsString = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        List<ObjectId> eventIds = new ArrayList<>();
        if (StringUtils.isNotBlank(eventIdsString)) {
            List<String> eventIdsList = Arrays.asList(StringUtils.split(eventIdsString, ","));
            if (eventIdsList != null && !eventIdsList.isEmpty()) {
                for (String eventId : eventIdsList) {
                    eventIds.add(ParamUtils.toObjectId(eventId));
                }
            }
        }

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }

        if (!eventIds.isEmpty()) {
            // params
            for (ObjectId eventId : eventIds) {
                if (EventDao.loadEvent(eventId) != null) {
                    EventDao.updateEventCancelled(eventId, true);
                    EventNotificationDao.updateEventNotificationListCancelledByEventId(eventId, true);
                } else {
                    // non dovrebbe mai passarci dato che passo gli id dalla pagina (?)
                    Spark.halt(HttpStatus.BAD_REQUEST_400);
                }
            }

        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };

    public static Route be_event_qrcode_generate = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("oid"));
        ObjectId printId = null;
        if (eventId != null) {
            Event event = null;
            try {
                event = EventDao.loadEvent(eventId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (event != null) {
                if (event.getQrcodeFileId() != null) {
                    return event.getQrcodeFileId();
                }
                String qrcode = event.getQrcode();
                if (event.getQrcode() == null) {
                    qrcode = RouteUtils.generateIdentifier();
                    event.setQrcode(qrcode);
                }
                // generate qrcode
                String textQr = RouteUtils.language(RouteUtils.baseUrl(request) + Paths.EVENT_BASE + "?qrcode=" + qrcode, Defaults.LANGUAGE);
                String qrCodeSvg = QrcodeCommons.generateQRCodeImage(textQr);
                if (qrCodeSvg != null) {
                    printId = EventDao.updateQrcodeImage(qrcode, eventId, qrCodeSvg);
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return printId;
    };

    public static Route be_event_poster_remove = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId eventId = ParamUtils.toObjectId(request.queryParams("oid"));
        if (eventId != null) {
            Event event = null;
            try {
                event = EventDao.loadEvent(eventId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (event != null) {
                event.setCoverImageId(null);
                EventDao.updateEvent(event);

                response.redirect(RouteUtils.contextPath(request) + Paths.BE_EVENT_EDIT + "?oid=" + event.getId().toString());
                return null;
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
    };

    ////////////
    // internals
    private static boolean isValidEvent(Event entity) {
        boolean valid = (entity != null)
                && StringUtils.isNotBlank(entity.getName());

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "event validation problem:\n"
                        + "- name " + entity.getName() + "\n"
                );
            } else {
                LOGGER.warn(
                        "event validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

    public static Location geocode(Event event) {
        Location location = null;

        if (event != null) {
            if (StringUtils.isNotBlank(event.getCity())
                    && StringUtils.isNotBlank(event.getAddress())
                    && StringUtils.isNotBlank(event.getProvinceCode())
                    && StringUtils.isNotBlank(event.getPostalCode())) {

                String address = event.getAddress() + ", " + event.getPostalCode() + " " + event.getCity() + ", " + event.getProvinceCode();
                location = Geocoder.geocode(address);
            }
        }

        return location;
    }

    public static boolean needNotifyChangeEvent(Event previous, Event updating) {
        return !(updating.getStartDate() == previous.getStartDate())
                || !StringUtils.equals(updating.getStartHour(), previous.getStartHour())
                || !StringUtils.equals(updating.getStatus(), previous.getStatus());
    }
}
