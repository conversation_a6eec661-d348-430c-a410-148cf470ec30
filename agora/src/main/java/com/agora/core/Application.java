package com.agora.core;

import com.agora.account.AccountController;
import com.agora.admin.AdminController;
import com.agora.analysis.AnalysisController;
import com.agora.api.ApiController;
import com.agora.area.AreaController;
import com.agora.category.CategoryController;
import com.agora.commons.CacheCommons;
import com.agora.commons.MailNotificationScheduler;
import com.agora.customers.CustomerController;
import com.agora.dashboard.DashboardController;
import com.agora.dashboard.login.LoginController;
import com.agora.data.DataController;
import com.agora.event.EventController;
import com.agora.eventreport.EventReportController;
import com.agora.eventrequest.EventRequestController;
import com.agora.home.HomeController;
import com.agora.homeslider.HomeSliderController;
import com.agora.mailnotification.MailnotificationController;
import com.agora.notification.NotificationController;
import com.agora.page.PageController;
import com.agora.pageclaim.PageClaimController;
import com.agora.pagereport.PageReportController;
import com.agora.post.PostController;
import com.agora.profile.ProfileController;
import com.agora.search.SearchlogController;
import com.agora.sponsorevent.SponsorEventController;
import com.agora.sponsorpage.SponsorPageController;
import com.agora.subcategory.SubcategoryController;
import com.agora.support.DownloadController;
import com.agora.support.ErrorController;
import com.agora.support.FileController;
import com.agora.support.ImageController;
import com.agora.support.PrintController;
import com.agora.support.UploadController;
import com.agora.util.EnvironmentUtils;
import com.agora.vendor.VendorController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Route;
import spark.Spark;
import static spark.Spark.notFound;
import spark.TemplateViewRoute;
import spark.servlet.SparkApplication;

/**
 *
 * <AUTHOR>
 */
public class Application implements SparkApplication {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void init() {

        // configure static resources
        // ... remember to put index.html in your static folder if you want a
        //     project default page
        if (EnvironmentUtils.isLocal()) {
            Spark.staticFiles.externalLocation("/projects/agora-bundle/agora/src/main/resources/public");
            // no caching
        } else {
            Spark.staticFileLocation("/public");
            Spark.staticFiles.expireTime(Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
        }

        // default exception handling (simply logs exceptions)
        Spark.exception(Exception.class, (exception, request, response) -> {
            logger.error("exception ", exception);
        });

        // clean-up useless slashes
        Spark.before("*", Filters.removeTrailingSlashes);

        // check connections (when mongodb or redis aren't working)
        Spark.before("*", Filters.checkConnections);

        // root default (when a static index.html doesn't exists)
        Spark.before("/", Filters.defaultRootRedirect);

        // not found
        notFound(ErrorController.notFound);

        // cache
        CacheCommons.reloadMostUsedTags();
        CacheCommons.reloadHomePages();
        // CacheCommons.reloadHomeEvents();

        // start mail notification scheduler (only in production)
        if (!EnvironmentUtils.isLocal()) {
            MailNotificationScheduler.start();
        }

        //////////////
        // f.e. routes

        // wall
        getHtml (Paths.WALL,                                         HomeController.wall);
        getHtml (Paths.HOME,                                         HomeController.home);
        getHtml (Paths.MEDIAKIT,                                     HomeController.mediakit);
        getHtml (Paths.EVENTS,                                       HomeController.events);
        getHtml (Paths.EVENTS_CATEGORY,                              HomeController.events);
        getHtml (Paths.ABOUT,                                        HomeController.about);
        getHtml (Paths.TOS,                                          HomeController.tos);
        getHtml (Paths.SITEMAP,                                      HomeController.sitemap);
        getHtml (Paths.RESULTS,                                      HomeController.results);
        post    (Paths.NOTIFICATION_ALL_READ,                        HomeController.notification_all_read);
        post    (Paths.NOTIFICATION_READ,                            HomeController.notification_read);
        //getHtml (Paths.CONTACTS,                                      HomeController.contacts);
        //post    (Paths.CONTACTS_SEND,                                 HomeController.contacts_send);

        // pages
        getHtml (Paths.PAGE_DETAIL,                                  PageController.page_detail);
        getHtml (Paths.PAGE_DIFF,                                    PageController.page_diff);
        post    (Paths.PAGE_DIFF_MERGE,                              PageController.page_diff_merge);
        getHtml (Paths.PAGE_BASE,                                    PageController.page);
        getHtml (Paths.PAGE_CLAIM,                                   PageController.page_claim);
        post    (Paths.PAGE_CLAIM_SEND,                              PageController.page_claim_send);
        getHtml (Paths.PAGE_ADD,                                     PageController.page_add);
        post    (Paths.PAGE_ADD_SAVE,                                PageController.page_add_save);
        getHtml (Paths.PAGE_EDIT,                                    PageController.page_edit);
        post    (Paths.PAGE_EDIT_SAVE,                               PageController.page_edit_save);
        post    (Paths.PAGE_FOLLOWER_TOGGLE,                         PageController.page_follower_toggle);
        post    (Paths.PAGE_NOTIFICATION_TOGGLE,                     PageController.page_notification_toggle);
        post    (Paths.PAGE_REMOVE,                                  PageController.page_remove);
        post    (Paths.PAGE_EVENT_REQUEST,                           PageController.page_request_event);
        post    (Paths.PAGE_EVENT_REQUEST_REMOVE,                    PageController.page_request_event_remove);
        post    (Paths.PAGE_REPORT_SEND,                             PageController.page_report_send);
        post    (Paths.PAGE_RELEASE,                                 PageController.page_release);
        getHtml (Paths.PAGE_IFRAME,                                  PageController.page_iframe);
        getHtml (Paths.PAGE_SEARCH_FRAME,                            PageController.page_search_iframe);

        // events
        getHtml (Paths.EVENT_DETAIL,                                 EventController.event_detail);
        getHtml (Paths.EVENT_BASE,                                   EventController.event);
        getHtml (Paths.EVENT_ADD,                                    EventController.event_add);
        post    (Paths.EVENT_ADD_SAVE,                               EventController.event_add_save);
        getHtml (Paths.EVENT_EDIT,                                   EventController.event_edit);
        post    (Paths.EVENT_EDIT_SAVE,                              EventController.event_edit_save);
        post    (Paths.EVENT_EDIT_FILE_REMOVE,                       EventController.event_edit_file_remove);
        getHtml (Paths.EVENT_CLONE,                                  EventController.event_clone);
        get     (Paths.EVENT_PAGE_APPROVAL,                          EventController.event_page_approval);
        getHtml (Paths.EVENT_PAGE_APPROVAL_RESULT,                   EventController.event_page_approval_result);
        post    (Paths.EVENT_FOLLOWER_TOGGLE,                        EventController.event_follower_toggle);
        post    (Paths.EVENT_NOTIFICATION_TOGGLE,                    EventController.event_notification_toggle);
        post    (Paths.EVENT_REMOVE,                                 EventController.event_remove);
        post    (Paths.EVENT_DISCONNECT_CHILDREN,                    EventController.event_disconnect_children);
        post    (Paths.EVENT_REPORT_SEND,                            EventController.event_report_send);
        getJson (Paths.EVENT_LIST_AJAX,                              EventController.event_list_ajax);
        getJson (Paths.EVENT_PAGES_AJAX,                             EventController.event_pages_ajax);
        getJson (Paths.EVENT_NAVIGATION_AJAX,                        EventController.event_navigation_ajax);



        // events
//        getHtml (Paths.NOTIFICATION_DETAIL,                          NotificationController.notification_detail);
        getHtml (Paths.NOTIFICATION_ADD,                             NotificationController.notification_add);
        post    (Paths.NOTIFICATION_ADD_SAVE,                        NotificationController.notification_add_save);
        getHtml (Paths.NOTIFICATION_EDIT,                            NotificationController.notification_edit);
        post    (Paths.NOTIFICATION_EDIT_SAVE,                       NotificationController.notification_edit_save);
        post    (Paths.NOTIFICATION_REMOVE,                          NotificationController.notification_remove);
        post    (Paths.NOTIFICATION_ACTIVE,                          NotificationController.notification_active);

        // events
//        getHtml (Paths.EVENT_DEMO,                                    EventController.event_demo);

        // access
        getHtml (Paths.ACCESS,                                        LoginController.access);
        getHtml (Paths.REGISTER,                                      LoginController.register);
        post    (Paths.REGISTER_SEND,                                 LoginController.register_send);

        post    (Paths.ACCESS_DO,                                     LoginController.access_do);
        get     (Paths.EXIT_DO,                                       LoginController.exit_do);
        getHtml (Paths.RECOVER,                                       LoginController.recover);
        getHtml (Paths.ACCOUNT_CONFIRM,                               LoginController.account_confirm);
        post    (Paths.ACCOUNT_CONFIRM_SEND,                          LoginController.account_confirm_send);

        // this GET avoid error on https://opensiteexplorer.org/dotbot
        get     (Paths.RECOVER_SEND,                                  LoginController.error_404);
        post    (Paths.RECOVER_SEND,                                  LoginController.recover_send);

        // account
        getHtml (Paths.ACCOUNT_INFO,                                  AccountController.account_info);
        post    (Paths.ACCOUNT_INFO_EDIT_SAVE,                        AccountController.account_info_edit_save);
        getHtml (Paths.ACCOUNT_PAGES,                                 AccountController.account_pages);
        post    (Paths.ACCOUNT_PAGE_SET_PRIMARY,                      AccountController.account_page_set_primary);
        getHtml (Paths.ACCOUNT_CALENDAR,                              AccountController.account_calendar);
        getHtml (Paths.ACCOUNT_NOTIFICATIONS,                         AccountController.account_notifications);
        post    (Paths.ACCOUNT_REMOVE,                                AccountController.account_remove);
        getHtml (Paths.ACCOUNT_DELETED,                               AccountController.account_deleted);
        post    (Paths.ACCOUNT_SECURITY_CHANGE_PASSWORD_SAVE,         AccountController.account_security_change_password_save);
        post    (Paths.ACCOUNT_SECURITY_KEYWORD_SAVE,                 AccountController.account_security_keyword_save);
        post    (Paths.ACCOUNT_SECURITY_USERNAME_SAVE,                AccountController.account_security_username_save);



        //////////////
        // b.e. routes
        // dashboard
        getHtml (Paths.DASHBOARD,                                     DashboardController.dashboard);

        // profile
        getHtml (Paths.PROFILE,                                       ProfileController.profile, FlashMessageType.successAndError);
        post    (Paths.PROFILE_SAVE,                                  ProfileController.profile_save);

        // firm
        getHtml (Paths.FIRM,                                          ProfileController.firm, FlashMessageType.successAndError);
        post    (Paths.FIRM_SAVE,                                     ProfileController.firm_save);
        post    (Paths.FIRM_RELOAD_PAGES_IDENTIFIER,                  ProfileController.firm_reload_pages_identifier);
        post    (Paths.FIRM_RELOAD_EVENTS_IDENTIFIER,                 ProfileController.firm_reload_events_identifier);

        // smtp
        getHtml (Paths.SMTP,                                          ProfileController.smtp, FlashMessageType.successAndError);
        post    (Paths.SMTP_SAVE,                                     ProfileController.smtp_save);

        // accesso
        getHtml (Paths.BE_LOGIN,                                      LoginController.login, FlashMessageType.error);
        getHtml (Paths.LOGIN,                                         LoginController.login, FlashMessageType.error);
        post    (Paths.LOGIN_DO,                                      LoginController.login_do);

        getHtml (Paths.FORGOT,                                        LoginController.forgot, FlashMessageType.successAndError);
        post    (Paths.FORGOT_SEND,                                   LoginController.forgot_send);

        get     (Paths.LOGOUT_DO,                                     LoginController.logout_do);

        // customers
        getHtml (Paths.CUSTOMERS,                                     CustomerController.customers, FlashMessageType.successAndError);
        getJson (Paths.CUSTOMERS_DATA,                                CustomerController.customers_data);
        getHtml (Paths.CUSTOMERS_ADD,                                 CustomerController.customers_add, FlashMessageType.successAndError);
        post    (Paths.CUSTOMERS_ADD_SAVE,                            CustomerController.customers_add_save);
        post    (Paths.CUSTOMERS_EDIT_SAVE,                           CustomerController.customers_edit_save);
        getHtml (Paths.CUSTOMER_VIEW,                                 CustomerController.customer_view, FlashMessageType.successAndError);
        post    (Paths.CUSTOMER_REMOVE,                               CustomerController.customer_remove);
        post    (Paths.CUSTOMER_ADDRESS_SAVE,                         CustomerController.customer_address_save);
        post    (Paths.CUSTOMER_ADDRESS_REMOVE,                       CustomerController.customer_address_remove);

        // vendors
        getHtml (Paths.VENDORS,                                       VendorController.vendors, FlashMessageType.successAndError);
        getHtml (Paths.VENDORS_ADD,                                   VendorController.vendors_add, FlashMessageType.error);
        post    (Paths.VENDORS_ADD_SAVE,                              VendorController.vendors_add_save);
        post    (Paths.VENDORS_EDIT_SAVE,                             VendorController.vendors_edit_save);
        getHtml (Paths.VENDOR_VIEW,                                   VendorController.vendor_view, FlashMessageType.successAndError);
        post    (Paths.VENDOR_REMOVE,                                 VendorController.vendor_remove);

        // label
        getHtml (Paths.LABELS,                                        AdminController.labels, FlashMessageType.successAndError);
        getJson (Paths.LABELS_DATA,                                   AdminController.labels_data);
        post    (Paths.LABELS_SAVE,                                   AdminController.labels_save);

        get     (Paths.LABELS_CLEAR,                                  AdminController.labels_clear);

        // area
        getHtml   (Paths.AREAS,                                       AreaController.areas, FlashMessageType.successAndError);
        getHtml   (Paths.AREA_EDIT,                                   AreaController.area_edit, FlashMessageType.error);
        post      (Paths.AREA_EDIT_SAVE,                              AreaController.area_edit_save);
        post      (Paths.AREA_REMOVE,                                 AreaController.area_remove);

        // categories
        getHtml   (Paths.CATEGORIES,                                  CategoryController.categories, FlashMessageType.successAndError);
        getHtml   (Paths.CATEGORY_EDIT,                               CategoryController.category_edit, FlashMessageType.error);
        post      (Paths.CATEGORY_EDIT_SAVE,                          CategoryController.category_edit_save);
        post      (Paths.CATEGORY_REMOVE,                             CategoryController.category_remove);

        // subcategories
        getHtml   (Paths.SUBCATEGORIES,                               SubcategoryController.subcategories, FlashMessageType.successAndError);
        getHtml   (Paths.SUBCATEGORY_EDIT,                            SubcategoryController.subcategory_edit, FlashMessageType.error);
        post      (Paths.SUBCATEGORY_EDIT_SAVE,                       SubcategoryController.subcategory_edit_save);
        post      (Paths.SUBCATEGORY_REMOVE,                          SubcategoryController.subcategory_remove);

        // post
        getHtml   (Paths.POSTS,                                       PostController.posts, FlashMessageType.successAndError);
        getHtml   (Paths.POST_EDIT,                                   PostController.post_edit, FlashMessageType.error);
        post      (Paths.POST_EDIT_SAVE,                              PostController.post_edit_save);
        post      (Paths.POST_REMOVE,                                 PostController.post_remove);
        post      (Paths.POST_IMAGE_SAVE,                             PostController.post_image_save);

        // evetnrequests
        getHtml (Paths.BE_EVENT_REQUESTS,                             EventRequestController.be_eventrequests);

        // pagereports
        getHtml (Paths.BE_PAGE_REPORTS,                               PageReportController.be_pagereports);

        // pagereports
        getHtml (Paths.BE_PAGE_CLAIMS,                                PageClaimController.be_pageclaims);

        // pagereports
        getHtml (Paths.BE_EVENT_REPORTS,                              EventReportController.be_eventreports);

        // searches
        getHtml (Paths.BE_SEARCHES,                                   SearchlogController.be_searches);

        // events
        getHtml   (Paths.BE_EVENTS,                                   EventController.be_events, FlashMessageType.successAndError);
        getJson   (Paths.BE_EVENTS_DATA,                              EventController.be_events_data);
        getHtml   (Paths.BE_EVENT_EDIT,                               EventController.be_event_edit, FlashMessageType.error);
        post      (Paths.BE_EVENT_EDIT_SAVE,                          EventController.be_event_edit_save);
        post      (Paths.BE_EVENT_REMOVE,                             EventController.be_event_remove);
        post      (Paths.BE_EVENT_REMOVE_MULTIPLE,                    EventController.be_event_remove_multiple);
        post      (Paths.BE_EVENT_QRCODE_GENERATE,                    EventController.be_event_qrcode_generate);
        get       (Paths.BE_EVENT_REMOVE_POSTER,                      EventController.be_event_poster_remove);


        // pages
        getHtml   (Paths.BE_PAGES,                                    PageController.be_pages, FlashMessageType.successAndError);
        getJson   (Paths.BE_PAGES_DATA,                               PageController.be_pages_data);
        getHtml   (Paths.BE_PAGE_EDIT,                                PageController.be_page_edit, FlashMessageType.error);
        post      (Paths.BE_PAGE_EDIT_SAVE,                           PageController.be_page_edit_save);
        post      (Paths.BE_PAGE_REMOVE,                              PageController.be_page_remove);
        post      (Paths.BE_PAGE_QRCODE_GENERATE,                     PageController.be_page_qrcode_generate);
        get       (Paths.BE_PAGE_REMOVE_POSTER,                       PageController.be_page_poster_remove);
        get       (Paths.BE_PAGE_REMOVE_PROFILE_IMAGE,                PageController.be_page_profile_image_remove);

        // wall slider
        getHtml   (Paths.HOME_SLIDERS,                                HomeSliderController.homeSliders, FlashMessageType.successAndError);
        getHtml   (Paths.HOME_SLIDER_EDIT,                            HomeSliderController.homeSlider_edit, FlashMessageType.error);
        post      (Paths.HOME_SLIDER_EDIT_SAVE,                       HomeSliderController.homeSlider_edit_save);
        post      (Paths.HOME_SLIDER_REMOVE,                          HomeSliderController.homeSlider_remove);

        // sponsor events
        getHtml   (Paths.SPONSOR_EVENT_COLLECTION,                    SponsorEventController.sponsor_event_collection, FlashMessageType.successAndError);
        getHtml   (Paths.SPONSOR_EVENT_EDIT,                          SponsorEventController.sponsor_event_edit, FlashMessageType.error);
        post      (Paths.SPONSOR_EVENT_EDIT_SAVE,                     SponsorEventController.sponsor_event_edit_save);
        post      (Paths.SPONSOR_EVENT_REMOVE,                        SponsorEventController.sponsor_event_remove);

        // sponsor pages
        getHtml   (Paths.SPONSOR_PAGE_COLLECTION,                     SponsorPageController.sponsor_page_collection, FlashMessageType.successAndError);
        getHtml   (Paths.SPONSOR_PAGE_EDIT,                           SponsorPageController.sponsor_page_edit, FlashMessageType.error);
        post      (Paths.SPONSOR_PAGE_EDIT_SAVE,                      SponsorPageController.sponsor_page_edit_save);
        post      (Paths.SPONSOR_PAGE_REMOVE,                         SponsorPageController.sponsor_page_remove);

        getHtml (Paths.PAGES_UPLOAD,                                  UploadController.pages_upload, FlashMessageType.successAndError);
        getHtml (Paths.PAGES_UPLOAD_FOTO,                             UploadController.pages_upload_foto, FlashMessageType.successAndError);
        post    (Paths.PAGES_UPLOAD_VERIFY,                           UploadController.pages_upload_verify);
        post    (Paths.PAGES_UPLOAD_SAVE,                             UploadController.pages_upload_save);

        getHtml (Paths.EVENTS_UPLOAD,                                 UploadController.events_upload, FlashMessageType.successAndError);
        post    (Paths.EVENTS_UPLOAD_VERIFY,                          UploadController.events_upload_verify);
        post    (Paths.EVENTS_UPLOAD_SAVE,                            UploadController.events_upload_save);

        getHtml (Paths.MAILNOTIFICATIONS,                             MailnotificationController.mailnotifications, FlashMessageType.successAndError);
        get     (Paths.MAILNOTIFICATIONS_DATA_SEND,                   MailnotificationController.mailnotifications_data_send);
        getJson (Paths.MAILNOTIFICATION_STATUS,                       MailnotificationController.mailnotification_status);
        getHtml (Paths.MAILNOTIFICATION_VIEW,                         MailnotificationController.mailnotification_view, FlashMessageType.successAndError);

        // analysis
        getHtml   (Paths.ANALYSIS_DATA,                               AnalysisController.analysis_data);
        getHtml   (Paths.ANALYSIS_PIVOT,                              AnalysisController.analysis_pivot);

        // support
        get     (Paths.IMAGE,                                         ImageController.image);
        get     (Paths.IMAGE_SYSTEM,                                  ImageController.imagenew);
        get     (Paths.THUMBNAIL,                                     ImageController.thumbnail);
        get     (Paths.PRINT,                                         PrintController.print);
        get     (Paths.PRINT_TEMP,                                    PrintController.print_temp);
        get     (Paths.PRINT_TEMP_TXT,                                PrintController.print_temp_txt);
        get     (Paths.PRINT_TEMP_CSV,                                PrintController.print_temp_csv);
        get     (Paths.FILE,                                          FileController.file);
        get     (Paths.DOWNLOAD,                                      DownloadController.download);

        // geocoder
        get     (Paths.GEOCODER_CLEAR,                                AdminController.geocoder_clear);

        // data
        getJson (Paths.DATA_CITIES,                                   DataController.data_cities);
        getJson (Paths.DATA_PAGES,                                    DataController.data_pages);
        getJson (Paths.DATA_SEARCH_EVENTS,                            DataController.data_events);
        getJson (Paths.DATA_SEARCH_EVENTS_CONTAINER,                  DataController.data_events_container);
        getJson (Paths.DATA_SEARCH,                                   DataController.data_search);
        getJson (Paths.DATA_CUSTOMERS,                                DataController.data_customers);
        getJson (Paths.DATA_CITIES_WITH_PROVINCE,                     DataController.data_cities_with_province);
        getJson (Paths.DATA_SUBCATEGORIES,                            DataController.data_subcategories);

        getJson (Paths.DATA_TAG_PAGE,                                 DataController.data_tag_page);
        getJson (Paths.DATA_TAG_EVENT,                                DataController.data_tag_event);
        getJson (Paths.DATA_USER,                                     DataController.data_user);

        getJson (Paths.DATA_CITIES_AC,                                DataController.data_cities_ac);

        getJson (Paths.API_PING,                                      ApiController.ping);
        getJson (Paths.API_PAGES,                                     ApiController.pages);
        getJson (Paths.API_PAGE,                                      ApiController.page);
        getJson (Paths.API_EVENTS,                                    ApiController.events);
        getJson (Paths.API_EVENT,                                     ApiController.event);
        post    (Paths.API_PAGES_OPERATE,                             ApiController.pages_operate);
        post    (Paths.API_EVENTS_OPERATE,                            ApiController.events_operate);
        post    (Paths.API_RELOAD_CACHE,                              ApiController.reload_cache);
    }

    @Override
    public void destroy() {
        // stop mail notification scheduler
        MailNotificationScheduler.stop();

        Manager.destroy();
    }

    private void get(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.get(pth, route);
        }
    }

    private void getHtml(String path, TemplateViewRoute route) {
        getHtml(path, route, FlashMessageType.none);
    }

    private void getHtml(String path, TemplateViewRoute route, FlashMessageType flash) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.get(pth, route, Manager.engine);
            if (flash != null) switch (flash) {
                case successAndError:
                    Spark.get(Paths.success(pth), route, Manager.engine);
                    Spark.get(Paths.error(pth), route, Manager.engine);
                    break;
                case success:
                    Spark.get(Paths.success(pth), route, Manager.engine);
                    break;
                case error:
                    Spark.get(Paths.error(pth), route, Manager.engine);
                    break;
                default:
                    break;
            }
        }
    }

    private void getJson(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.get(pth, route, Manager.jsonTransformer);
        }
    }

    private void post(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.post(pth, route);
        }
    }

    private void postJson(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.post(pth, route, Manager.jsonTransformer);
        }
    }

}
