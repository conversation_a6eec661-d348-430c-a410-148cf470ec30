(function () {
    bindEventFollow();
    bindDisconnectChildren();
    setTimeout(function () {
        initEventFiltering();
        initEventNavigation();
    }, 250);
})();

function bindEventFollow() {
    $('.event-add-follow').off();
    $('.event-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#eventFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var eventId = event.target.getAttribute('data-event-id');
        if (!eventId) {
            eventId = event.target.parentNode.getAttribute('data-event-id');
        }
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }
        
        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }
        
        if (user === 'unlogged') {
            
            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.registered.event'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
            });

            
            return false;
        }
        
        url.removeSearch("eventId");
        url.addSearch("eventId", eventId);

        
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        var msg = label('common.event.added');
                        if (value === 'active') {
                            var msg = label('common.event.removed');
                        }
                       
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            window.location.reload();
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
        });                    

        return false;
    });
}

// Event filtering functionality
function initEventFiltering() {
    // Check if there are container events to filter
    var containerEvents = $('.containerEvents');
    if (containerEvents.length === 0) {
        console.log('No container events found, filtering not needed');
        return;
    }

    // Get current event ID from the page
    var eventId = getEventIdFromPage();
    if (!eventId) {
        console.warn('Event ID not found, filtering disabled');
        // Hide filter controls if no event ID
        $('.card-header').has('#pageFilter, #pageFilterHorizontal').hide();
        return;
    }

    // Load pages for dropdown
    loadPagesForEvent(eventId);

    // Check for URL parameters and auto-select
    // checkUrlParameters();

    // Bind filter change events
    bindFilterEvents();

    // Set up browser navigation handling
    setupBrowserNavigation();
}

function getEventIdFromPage() {
    /*// Try to get event ID from various possible sources
    var eventId = null;

    // Try from meta tag
    var metaTag = $('meta[name="event-id"]');
    if (metaTag.length > 0) {
        eventId = metaTag.attr('content');
    }

    // Try from data attribute on body or main container
    if (!eventId) {
        eventId = $('body').data('event-id') || $('.main-content').data('event-id');
    }

    // Try from event follow button data attribute
    if (!eventId) {
        var followButton = $('.event-add-follow');
        if (followButton.length > 0) {
            eventId = followButton.attr('data-event-id');
        }
    }

    // Try from hidden input or data attribute
    if (!eventId) {
        var hiddenInput = $('input[name="eventId"]');
        if (hiddenInput.length > 0) {
            eventId = hiddenInput.val();
        }
    }

    // Try to extract from URL path (last resort)
    if (!eventId) {
        var pathParts = window.location.pathname.split('/');
        var eventIndex = -1;
        for (var i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'evento' || pathParts[i] === 'event') {
                eventIndex = i;
                break;
            }
        }
        if (eventIndex >= 0 && pathParts.length > eventIndex + 1) {
            // This is the event identifier, we'll need to make an API call to get the ID
            // For now, we'll use the identifier as a fallback
            var eventIdentifier = pathParts[eventIndex + 1];
            if (eventIdentifier && eventIdentifier.length > 0) {
                // We could make an API call here to convert identifier to ID
                // For now, we'll return the identifier and handle it in the API calls
                eventId = eventIdentifier;
            }
        }
    }*/

    var eventId = $('#eventId').text();
    return eventId;
}

function loadPagesForEvent(eventId) {
    var url = new URI($('#eventPagesUri').attr('href'));
    url.addSearch('eventId', eventId);

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            var parsedResponse = JSON.parse(response);
            if (parsedResponse.success && parsedResponse.pages) {
                populatePageDropdowns(parsedResponse.pages);

                checkUrlParameters();
            } else {
                console.warn('Failed to load pages:', parsedResponse.error || 'Unknown error');
                // Hide filter controls if pages can't be loaded
                $('#pageFilter, #pageFilterHorizontal').closest('.col-md-6').hide();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading pages:', error);
            // Hide filter controls if there's an error
            $('#pageFilter, #pageFilterHorizontal').closest('.col-md-6').hide();
        }
    });
}

function populatePageDropdowns(pages) {
    var pageOptions = '<option value="">' + (label('common.all.pages') || 'All Pages') + '</option>';

    pages.forEach(function(page) {
        var imageHtml = '';
        if (page.profileImageId) {
            imageHtml = '<img src="' + getImageUrl(page.profileImageId) + '" alt="' + page.name + '" style="width: 20px; height: 20px; border-radius: 50%; margin-right: 8px;">';
        }

        pageOptions += '<option value="' + page.id + '" data-image="' + (page.profileImageId || '') + '">' + imageHtml +
                      page.name + '</option>';
    });

    // Populate both dropdowns
    $('#pageFilter, #pageFilterHorizontal').html(pageOptions);
}

function getImageUrl(imageId) {
    // Build image URL based on the application's image system
    var basePath = $("#imageUri").attr("href");
    return basePath + '?oid=' + imageId;
}

function bindFilterEvents() {
    // Bind change events to both filter sets
    $('#pageFilter, #pageFilterHorizontal').on('change', function() {
        var filterId = $(this).attr('id');
        var otherFilterId = filterId === 'pageFilter' ? 'pageFilterHorizontal' : 'pageFilter';

        // Sync the other dropdown
        $('#' + otherFilterId).val($(this).val());

        // Apply filters
        applyFilters();
    });

    $('#sortOrder, #sortOrderHorizontal').on('change', function() {
        var filterId = $(this).attr('id');
        var otherFilterId = filterId === 'sortOrder' ? 'sortOrderHorizontal' : 'sortOrder';

        // Sync the other dropdown
        $('#' + otherFilterId).val($(this).val());

        // Apply filters
        applyFilters();
    });
}

function applyFilters() {
    var pageId = $('#pageFilter').val() || $('#pageFilterHorizontal').val();
    var sortOrder = $('#sortOrder').val() || $('#sortOrderHorizontal').val();

    // Show loading state
    showLoadingState();

    var url = new URI($('#eventListUri').attr('href'));
    var eventId = getEventIdFromPage();
    if (eventId) {
        url.addSearch('eventId', eventId);
    }
    if (pageId) {
        url.addSearch('pageId', pageId);
    }
    if (sortOrder) {
        url.addSearch('sortOrder', sortOrder);
    }

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            hideLoadingState();

            var parsedResponse = JSON.parse(response);
            if (parsedResponse.success && parsedResponse.events) {
                updateEventsList(parsedResponse.events);
                updateUrlState(pageId, sortOrder);

                // Show success feedback for filtered results
                if (pageId || (sortOrder && sortOrder !== 'desc')) {
                    showFilterFeedback(parsedResponse.events.length, pageId, sortOrder);
                }
            } else {
                console.warn('Failed to load events:', parsedResponse.error || 'Unknown error');
                showErrorState();
            }
        },
        error: function(xhr, status, error) {
            hideLoadingState();
            console.error('Error loading events:', error);
            showErrorState();
        }
    });
}

function updateEventsList(events) {
    // Update both vertical and horizontal layouts
    updateEventsContainer('.containerEvents .oneEvent', events);
}

function updateEventsContainer(containerSelector, events) {
    var container = $(containerSelector);
    if (container.length === 0) {
        return;
    }

    // Clear existing events
    container.empty();

    if (events.length === 0) {
        container.html('<div class="text-center py-4"><p class="text-muted">' +
                      (label('common.no.events.found') || 'No events found') + '</p></div>');
        return;
    }

    // Build events HTML
    var eventsHtml = '';
    events.forEach(function(event, index) {
        var isPastEvent = event.endDate ? (new Date(event.endDate) < new Date()) :
                         (event.startDate ? (new Date(event.startDate) < new Date()) : false);

        var eventPathBase = $('#eventPathBaseUri').attr('href');

        var eventHtml = '<div class="row">' +
            '<div class="d-sm-flex align-items-center ' + (isPastEvent ? 'opacity-50' : '') + '">' +
                '<div class="avatar avatar-xl">' +
                    '<a href="' + eventPathBase + '/' + event.identifier + '" class="eventlink">';

        // Event image
        if (event.coverImageId) {
            eventHtml += '<img class="avatar-img border border-white border-3" src="' + getImageUrl(event.coverImageId) + '" alt="' + event.name + '">';
        } else {
            eventHtml += '<img class="avatar-img border border-white border-3" src="/fe/images/bg/placeholder-event.jpg" alt="' + event.name + '">';
        }

        eventHtml += '</a></div>' +
            '<div class="ms-sm-4 mt-2 mt-sm-0">' +
                '<h5 class="mb-1"><a href="' + eventPathBase + '/' + event.identifier + '">' + event.name + '</a></h5>' +
                '<ul class="nav nav-stack small">';

        // Event date and time
        if (event.startDate) {
            var startDate = new Date(event.startDate);
            eventHtml += '<li class="nav-item">' +
                '<i class="bi bi-calendar-check pe-1"></i>' + formatEventDate(startDate) +
                (event.startHour ? ' ' + event.startHour : '') + '</li>';
        }

        // Event location
        if (event.city) {
            eventHtml += '<li class="nav-item"><i class="bi bi-geo-alt pe-1"></i>' + event.city + '</li>';
        }

        // Follower count
        if (event.followerCount > 0) {
            eventHtml += '<li class="nav-item"><i class="bi bi-people pe-1"></i>' + event.followerCount + ' ' +
                        (label('common.participants') || 'participants') + '</li>';
        }

        eventHtml += '</ul></div></div></div>';

        // Add separator except for last item
        if (index < events.length - 1) {
            eventHtml += '<hr>';
        }

        eventsHtml += eventHtml;
    });

    container.html(eventsHtml);
}

function formatEventDate(date) {
    // Simple date formatting - can be enhanced based on locale
    var options = { weekday: 'short', day: 'numeric', month: 'short', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
}

function showLoadingState() {
    $('.containerEvents .oneEvent').html('<div class="text-center py-4">' +
        '<div class="spinner-border text-primary" role="status">' +
            '<span class="visually-hidden">Loading...</span>' +
        '</div>' +
        '<p class="mt-2">' + (label('common.loading') || 'Loading...') + '</p>' +
    '</div>');
}

function hideLoadingState() {
    // Loading state will be replaced by updateEventsList
}

function showErrorState() {
    $('.containerEvents .oneEvent').html('<div class="text-center py-4">' +
        '<i class="bi bi-exclamation-triangle text-warning fs-1"></i>' +
        '<p class="mt-2 text-muted">' + (label('common.error.loading') || 'Error loading events') + '</p>' +
        '<button class="btn btn-outline-primary btn-sm" onclick="applyFilters()">' +
            (label('common.retry') || 'Retry') + '</button>' +
    '</div>');
}

function updateUrlState(pageId, sortOrder) {
    // Update browser URL without page reload
    var url = new URL(window.location);
    var originalUrl = url.toString();

    if (pageId) {
        url.searchParams.set('pageId', pageId);
    } else {
        url.searchParams.delete('pageId');
    }

    if (sortOrder && sortOrder !== 'desc') {
        url.searchParams.set('sortOrder', sortOrder);
    } else {
        url.searchParams.delete('sortOrder');
    }

    var newUrl = url.toString();

    // Only update URL if it has actually changed
    if (originalUrl !== newUrl && history.pushState) {
        // Create state object for better history management
        var state = {
            pageId: pageId || null,
            sortOrder: sortOrder || 'desc',
            timestamp: Date.now()
        };

        history.pushState(state, null, newUrl);
    }
}

function checkUrlParameters() {
    // Check URL for pageId and sortOrder parameters
    var url = new URL(window.location);
    var pageId = url.searchParams.get('pageId');
    var sortOrder = url.searchParams.get('sortOrder');

    if (pageId) {
        $('#pageFilter, #pageFilterHorizontal').val(pageId);
    }

    if (sortOrder) {
        $('#sortOrder, #sortOrderHorizontal').val(sortOrder);
    }

    // Apply filters if parameters were found
    if (pageId || sortOrder) {
        applyFilters();
    }
}

function showFilterFeedback(eventCount, pageId, sortOrder) {
    // Create a temporary feedback message
    var message = '';
    if (pageId) {
        var pageName = $('#pageFilter option[value="' + pageId + '"]').text() ||
                      $('#pageFilterHorizontal option[value="' + pageId + '"]').text();
        message += (label('common.filtered.by.page') || 'Filtered by page') + ': ' + pageName;
    }

    if (sortOrder && sortOrder !== 'desc') {
        if (message) message += ' | ';
        message += (label('common.sorted.by') || 'Sorted by') + ': ' +
                  (sortOrder === 'asc' ?
                   (label('common.oldest.first') || 'Oldest first') :
                   (label('common.newest.first') || 'Newest first'));
    }

    if (message) {
        message += ' (' + eventCount + ' ' + (label('common.events') || 'events') + ')';

        // Show temporary feedback
        var feedback = $('<div class="alert alert-info alert-dismissible fade show mt-2" role="alert">' +
            '<small>' + message + '</small>' +
            '<button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert"></button>' +
            '</div>');

        // Insert after filter controls
        $('.card-header').has('#pageFilter, #pageFilterHorizontal').after(feedback);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            feedback.fadeOut(function() {
                feedback.remove();
            });
        }, 5000);
    }
}

function setupBrowserNavigation() {
    // Handle browser back/forward navigation
    window.addEventListener('popstate', function(event) {
        // Re-read URL parameters and apply filters
        var url = new URL(window.location);
        var pageId = url.searchParams.get('pageId');
        var sortOrder = url.searchParams.get('sortOrder');

        // Update dropdown values without triggering change events
        $('#pageFilter, #pageFilterHorizontal').off('change');
        $('#sortOrder, #sortOrderHorizontal').off('change');

        if (pageId) {
            $('#pageFilter, #pageFilterHorizontal').val(pageId);
        } else {
            $('#pageFilter, #pageFilterHorizontal').val('');
        }

        if (sortOrder) {
            $('#sortOrder, #sortOrderHorizontal').val(sortOrder);
        } else {
            $('#sortOrder, #sortOrderHorizontal').val('desc');
        }

        // Re-bind events
        bindFilterEvents();

        // Apply filters based on URL state
        applyFiltersFromUrl(pageId, sortOrder);
    });
}

function applyFiltersFromUrl(pageId, sortOrder) {
    // Apply filters without updating URL (to avoid infinite loop)
    showLoadingState();

    // Build URL for events endpoint
    var url = new URI($('#eventListUri').attr('href'));
    var eventId = getEventIdFromPage();
    if (eventId) {
        url.addSearch('eventId', eventId);
    }
    if (pageId) {
        url.addSearch('pageId', pageId);
    }
    if (sortOrder) {
        url.addSearch('sortOrder', sortOrder);
    }

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            hideLoadingState();

            if (response.success && response.events) {
                updateEventsList(response.events);
                // Don't update URL state here since we're responding to URL change

                // Show success feedback for filtered results
                if (pageId || (sortOrder && sortOrder !== 'desc')) {
                    showFilterFeedback(response.events.length, pageId, sortOrder);
                }
            } else {
                console.warn('Failed to load events:', response.error || 'Unknown error');
                showErrorState();
            }
        },
        error: function(xhr, status, error) {
            hideLoadingState();
            console.error('Error loading events:', error);
            showErrorState();
        }
    });
}

function initEventNavigation() {
    // Check if this event has a parent (is part of a series)
    var parentId = $('#eventParentId').text();
    var currentEventId = $('#eventId').text();

    if (!parentId || !currentEventId) {
        return; // No navigation needed
    }

    // Load navigation data
    loadEventNavigation(parentId, currentEventId);
}

function loadEventNavigation(parentId, currentEventId) {
    var url = new URI($('#eventNavigationUri').attr('href'));
    if (!url) {
        console.error('Missing navigation URL');
        return;
    }

    url.addSearch('parentId', parentId);
    url.addSearch('currentEventId', currentEventId);

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            var parsedResponse = JSON.parse(response);
            if (parsedResponse.success) {
                setupNavigationButtons(parsedResponse.previous, parsedResponse.next);
            } else {
                console.warn('Failed to load navigation:', parsedResponse.error || 'Unknown error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading navigation:', error);
        }
    });
}

function setupNavigationButtons(previousEvent, nextEvent) {
    var eventBaseUrl = $('#eventPathBaseUri').attr('href');

    // Setup previous button (both vertical and horizontal layouts)
    if (previousEvent) {
        var prevUrl = eventBaseUrl + '/' + previousEvent.identifier;
        $('#prevEventBtn, #prevEventBtnHorizontal').show().off('click').on('click', function(e) {
            e.preventDefault();
            window.location.href = prevUrl;
        });
    }

    // Setup next button (both vertical and horizontal layouts)
    if (nextEvent) {
        var nextUrl = eventBaseUrl + '/' + nextEvent.identifier;
        $('#nextEventBtn, #nextEventBtnHorizontal').show().off('click').on('click', function(e) {
            e.preventDefault();
            window.location.href = nextUrl;
        });
    }
}

function bindDisconnectChildren() {
    $('#disconnectChildrenBtn, #disconnectChildrenBtnHorizontal').off('click').on('click', function(event) {
        event.preventDefault();

        var eventId = $(this).data('event-id');
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }

        var url = $('#eventDisconnectChildrenUri').attr('href');
        if (!url) {
            console.error('missing disconnect URL');
            return false;
        }

        // Show confirmation dialog
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="bi bi-exclamation-triangle icon-2x"></i><br/><br/> ' + label('event.disconnect.children.confirm.title'),
            content: label('event.disconnect.children.confirm.message'),
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                cancel: {
                    text: label('common.cancel'),
                    btnClass: 'btn-default',
                    action: function () {
                        // Do nothing, just close
                    }
                },
                confirm: {
                    text: label('common.confirm'),
                    btnClass: 'btn-danger',
                    action: function () {
                        // Perform the disconnect operation
                        $.blockUI();
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: { oid: eventId },
                            success: function(response) {
                                $.unblockUI();
                                // Reload the page to show updated state
                                location.reload();
                            },
                            error: function(xhr, status, error) {
                                $.unblockUI();
                                console.error('Error disconnecting children:', error);
                                alert('Errore durante lo scollegamento degli eventi. Riprova più tardi.');
                            }
                        });
                    }
                }
            }
        });
    });
}
